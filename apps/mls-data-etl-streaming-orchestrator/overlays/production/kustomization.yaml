apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - mls-data-etl-streaming-orchestrator-dd-metric.yaml
patches:
  - path: deploy.yaml
  - path: hpa.yaml
  - path: sa.yaml
  - path: ingress-internal.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-streaming-orchestrator
    newTag: v0.5.11 # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-production:tag"}
