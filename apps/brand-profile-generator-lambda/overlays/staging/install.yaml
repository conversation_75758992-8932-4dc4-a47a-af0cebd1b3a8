apiVersion: batch/v1
kind: Job
metadata:
  name: brand-profile-generator-lambda-installer-
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: brand-profile-generator-lambda-installer-sa
      labels:
        tags.datadoghq.com/version: v0.1.8 # {"lp-deploy-tag-updater:version": "brand-profile-generator"}
    spec:
      initContainers:
        - name: grab-config
          env:
            - name: CONFIG_TEMPLATE_PATH
              value: packages/brand-profile-generator/src/config/config-template.yaml
