apiVersion: apps/v1
kind: Deployment
metadata:
  name: buyer-seller-consumer-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
    spec:
      containers:
        - name: buyer-seller-consumer
          image: luxurypresence/app
          env:
            - name: JOB_TYPE
              value: "CONSUMER"
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-buyerseller-long-life-v4#username}:${vault:database/creds/postgres-staging-buyerseller-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}?schema=buyerseller'
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-buyerseller-long-life-v4#username}:${vault:database/creds/postgres-staging-buyerseller-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}?schema=buyerseller'
            - name: VAULT_DISASTER_RECOVERY_PG_CONNECTION_STRING
              valueFrom:
                $patch: delete
            - name: GOOGLE_CLIENT_ID
              value: vault:secret/data/staging/buyer-seller#GOOGLE_CLIENT_ID
            - name: FACEBOOK_APP_ID
              value: vault:secret/data/staging/buyer-seller#FACEBOOK_APP_ID
            - name: JWT_SECRET
              value: vault:secret/data/staging/buyer-seller#JWT_SECRET
            - name: LAUNCHDARKLY_KEY
              value: vault:secret/data/staging/standard#LAUNCHDARKLY_KEY
            - name: SEARCH_SERVICE_USER
              value: vault:secret/data/staging/buyer-seller#SEARCH_SERVICE_USER
            - name: SEARCH_SERVICE_PASSWORD
              value: vault:secret/data/staging/buyer-seller#SEARCH_SERVICE_PASSWORD
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: DEPLOYMENT_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['deployment']
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service=$(OTEL_SERVICE_NAME),k8s.pod.name=$(POD_NAME),k8s.namespace.name=$(POD_NAMESPACE),k8s.node.name=$(NODE_NAME),k8s.deployment.name=$(DEPLOYMENT_NAME),testing1=$(OTEL_SERVICE_NAME),testing2=$(DEPLOYMENT_NAME)
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: '1'
              memory: '650Mi'
            requests:
              cpu: '100m'
              memory: '356Mi'
