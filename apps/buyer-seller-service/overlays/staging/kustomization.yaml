apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - cron-job-instant.yaml
  - cron-job-daily.yaml
  - cron-job-weekly.yaml
  - cron-job-monthly.yaml
  - cron-job-mobile.yaml
  - cron-job-mobile-daily.yaml
patches:
  - path: deploy.yaml
  - path: ingress.yaml
  - path: sa.yaml
  - path: load-buyer-seller-migration-job.yaml
  - path: ingress-internal.yaml
  - path: consumer-deploy.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: buyer-seller-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-buyer-seller-migration
    newName: luxurypresence/load-buyer-seller-migration
    newTag: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
  - name: luxurypresence/app
    newName: luxurypresence/buyer-seller-service
    newTag: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}

transformers:
  - version-suffixer.yaml
