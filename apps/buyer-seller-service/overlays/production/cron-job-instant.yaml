apiVersion: batch/v1
kind: CronJob
metadata:
  name: buyer-seller-service-cron-job-instant
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
    tags.datadoghq.com/service: buyer-seller-service-cron-job-instant
spec:
  schedule: "01 * * * *"
  jobTemplate:
    spec:
      backoffLimit: 1
      template:
        metadata:
          annotations:
            vault.security.banzaicloud.io/vault-role: buyer-seller-service-prod
            cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
            karpenter.sh/do-not-disrupt: "true"
          labels:
            tags.datadoghq.com/env: production
            tags.datadoghq.com/version: v3.4.5 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
            luxurypresence/repository-name: client-applications
            luxurypresence/package-name: buyer-seller-service
        spec:
          serviceAccountName: buyer-seller-service
          initContainers:
            - name: grab-config
              image: luxurypresence/app
              env:
                - name: DEFAULT_CONFIG_TEMPLATE_PATHS
                  value: src/config/config-template.yaml,src/config-template.yaml,config-template.yaml,config/config-template.yaml
              command:
                - /bin/sh
                - -e
                - -c
                - |
                  # Initialize the not found paths variable
                  CONFIG_TEMPLATE_NOT_FOUND_PATHS=""

                  if [ -f "$CONFIG_TEMPLATE_PATH" ]; then
                    echo "Using template path specified with env: $CONFIG_TEMPLATE_PATH"
                    cp $CONFIG_TEMPLATE_PATH /template/config-template.yaml
                    exit 0
                  else
                    # Add the specific path to not found paths if it was set
                    if [ ! -z "$CONFIG_TEMPLATE_PATH" ]; then
                      CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_PATH"
                    fi
                  fi

                  I=1
                  while
                    CONFIG_TEMPLATE_PATH=$(echo "$DEFAULT_CONFIG_TEMPLATE_PATHS" | cut -d"," -f$I) || "";
                    if [ -f "$CONFIG_TEMPLATE_PATH" ]; then
                      echo "Discovered config in $CONFIG_TEMPLATE_PATH"
                      cp $CONFIG_TEMPLATE_PATH /template/config-template.yaml
                      break
                    else
                      # Add to not found paths if not empty
                      if [ ! -z "$CONFIG_TEMPLATE_PATH" ]; then
                        if [ -z "$CONFIG_TEMPLATE_NOT_FOUND_PATHS" ]; then
                          CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_PATH"
                        else
                          CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_NOT_FOUND_PATHS,$CONFIG_TEMPLATE_PATH"
                        fi
                      fi
                    fi
                    I=$((I+1))
                    [ ! -z "$CONFIG_TEMPLATE_PATH" ] || break;
                  do : ; done

                  # If no config template was found, create a fallback file
                  if [ ! -f "/template/config-template.yaml" ]; then
                    echo "No config template found in any of the searched paths"
                    echo "Creating fallback config-template.yaml with searched paths"
                    echo "SEARCHED_PATHS=$CONFIG_TEMPLATE_NOT_FOUND_PATHS" > /template/config-template.yaml
                  fi
              volumeMounts:
                - mountPath: /template
                  name: app-template
            - name: render-config
              image: luxurypresence/config-generator:3.8.0
              env:
                - name: ENVIRONMENT
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: APP_ENV
                - name: BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: BASE_DOMAIN
                - name: INTERNAL_BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: INTERNAL_BASE_DOMAIN
                - name: ACCOUNT_ID
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: ACCOUNT_ID
                - name: VAULT_ADDR
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_ADDR
                - name: VAULT_ENV
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_ENV
                - name: VAULT_PATH
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_PATH
                - name: SA_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: spec.serviceAccountName
                - name: VAULT_ROLE # Take precedence over SA_NAME if specified
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.annotations['vault.security.banzaicloud.io/vault-role']
                - name: VAULT_LOG_LEVEL
                  value: ERROR
                - name: RELEASE_NOTIFICATION_URL
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: RELEASE_NOTIFICATION_URL
                - name: REPOSITORY_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['luxurypresence/repository-name']
                - name: PACKAGE_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['luxurypresence/package-name']
                - name: VERSION
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['tags.datadoghq.com/version']
              volumeMounts:
                - mountPath: /workspace/config-template.yaml
                  name: app-template
                  subPath: config-template.yaml
                - mountPath: /workspace/result
                  name: app-config
          containers:
            - name: buyer-seller-service-cron-job-instant
              image: luxurypresence/app
              env:
                - name: ENVIRONMENT
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: APP_ENV
                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      fieldPath: status.hostIP
                - name: DD_ENV
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['tags.datadoghq.com/env']
                - name: DD_SERVICE
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['tags.datadoghq.com/service']
                - name: DD_VERSION
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['tags.datadoghq.com/version']
                - name: PG_CONNECTION_STRING
                  value: "postgresql://${vault:database/creds/postgres-production-buyerseller-long-life-v3#username}:${vault:database/creds/postgres-production-buyerseller-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}?schema=buyerseller"
                - name: PG_READER_CONNECTION_STRING
                  value: "postgresql://${vault:database/creds/postgres-production-buyerseller-long-life-v3#username}:${vault:database/creds/postgres-production-buyerseller-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}?schema=buyerseller"
                - name: GOOGLE_CLIENT_ID
                  value: vault:secret/data/production/buyer-seller#GOOGLE_CLIENT_ID
                - name: FACEBOOK_APP_ID
                  value: vault:secret/data/production/buyer-seller#FACEBOOK_APP_ID
                - name: JWT_SECRET
                  value: vault:secret/data/production/buyer-seller#JWT_SECRET
                - name: LAUNCHDARKLY_KEY
                  value: vault:secret/data/production/standard#LAUNCHDARKLY_KEY
                - name: SEARCH_SERVICE_USER
                  value: vault:secret/data/production/buyer-seller#SEARCH_SERVICE_USER
                - name: SEARCH_SERVICE_PASSWORD
                  value: vault:secret/data/production/buyer-seller#SEARCH_SERVICE_PASSWORD
                - name: SCHEMA
                  value: "buyerseller"
                - name: BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: BASE_DOMAIN
                - name: INTERNAL_BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: INTERNAL_BASE_DOMAIN
                - name: JOB_TYPE
                  value: "INSTANT"
                - name: CONCURRENCY_NUMBER
                  value: "1"
                - name: HOST_IP
                  valueFrom:
                    fieldRef:
                      fieldPath: status.hostIP
                - name: OTEL_EXPORTER_OTLP_ENDPOINT
                  value: "http://$(HOST_IP):4318"
                - name: NODE_OPTIONS
                  value: "--max-old-space-size=2560"
                - name: VAULT_LOG_LEVEL
                  value: ERROR
                - name: DEFAULT_TAKE
                  value: "20000"
              resources:
                limits:
                  cpu: "1"
                  memory: "4Gi"
                requests:
                  cpu: "0.5"
                  memory: "4Gi"
              volumeMounts:
                - mountPath: /usr/src/app/packages/buyer-seller-service/.env
                  name: app-config
                  subPath: .env
                - mountPath: /usr/src/app/packages/buyer-seller-service/dist/config/config.json
                  name: app-config
                  subPath: config.json
                - mountPath: /home/<USER>/dist/config/config.json
                  name: app-config
                  subPath: config.json
                # Dotenv projects
                - mountPath: /usr/src/app/.env
                  name: app-config
                  subPath: .env
                # Convict JavaScript projects
                - mountPath: /usr/src/app/src/config/config.json
                  name: app-config
                  subPath: config.json
                - mountPath: /usr/src/app/config.json
                  name: app-config
                  subPath: config.json
                # Convict TypeScript projects
                - mountPath: /usr/src/app/dist/config/config.json
                  name: app-config
                  subPath: config.json
          volumes:
            - emptyDir: {}
              name: app-config
            - emptyDir: {}
              name: app-template
          imagePullSecrets:
            - name: image-pull-secret
          restartPolicy: Never
