apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - ingress-workato.yaml
patches:
  - path: deployment.yaml
  - path: load-sql-migration-job.yaml
  - path: sa.yaml
  - path: ingress.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: tenant-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-tenant-service-migration
    newName: luxurypresence/load-tenant-service-migration
    newTag: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}
  - name: luxurypresence/app
    newName: luxurypresence/tenant-service
    newTag: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}

transformers:
  - version-suffixer.yaml