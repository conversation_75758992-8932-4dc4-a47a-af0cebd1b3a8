apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}
    spec:
      containers:
        - name: tenant-service
          env:
            - name: DATABASE_NAME
              value: vault:secret/data/staging/database-info/main-v4#DB
              valueFrom:
                $patch: delete
            - name: DATABASE_HOST
              value: vault:secret/data/staging/database-info/main-v4#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/staging/database-info/main-v4#READER_HOST
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-staging-identity-long-life-v4#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-staging-identity-long-life-v4#username
              valueFrom:
                $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
