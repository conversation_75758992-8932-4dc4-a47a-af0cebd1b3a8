apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: tenant-service-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v1.44.18 # {"lp-deploy-tag-updater:version": "tenant-service"}
    spec:
      containers:
        - name: tenant-service
          env:
            - name: DATABASE_NAME
              value: vault:secret/data/production/database-info/main-v3#DB
              valueFrom:
                $patch: delete
            - name: DATABASE_HOST
              value: vault:secret/data/production/database-info/main-v3#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/production/database-info/main-v3#READER_HOST
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-production-identity-long-life-v3#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-production-identity-long-life-v3#username
              valueFrom:
                $patch: delete
          resources:
            limits:
              cpu: '1'
              memory: '615Mi'
            requests:
              cpu: '1'
              memory: '615Mi'
