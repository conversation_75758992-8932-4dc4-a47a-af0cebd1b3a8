apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-ai-agent-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.5.13 # {"$imagepolicy": "flux-system:mls-data-ai-agent-flux-image-policy-production:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-ai-agent-prod
        karpenter.sh/do-not-disrupt: "true"
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.5.13 # {"$imagepolicy": "flux-system:mls-data-ai-agent-flux-image-policy-production:tag"}
