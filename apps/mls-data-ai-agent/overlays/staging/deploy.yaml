apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-ai-agent-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: main-14909-f678b41 # {"$imagepolicy": "flux-system:mls-data-ai-agent-flux-image-policy-staging-new:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-ai-agent-staging
        karpenter.sh/do-not-disrupt: "true"
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: main-14909-f678b41 # {"$imagepolicy": "flux-system:mls-data-ai-agent-flux-image-policy-staging-new:tag"}
