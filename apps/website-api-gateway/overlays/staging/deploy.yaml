apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-api-gateway-deployment
  labels:
    tags.datadoghq.com/env: staging
    backstage.io/kubernetes-id: website-api-gateway
    tags.datadoghq.com/version: v8.38.0 # {"lp-deploy-tag-updater:version": "website-api-gateway"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: website-api-gateway
      labels:
        tags.datadoghq.com/env: staging
        backstage.io/kubernetes-id: website-api-gateway
        tags.datadoghq.com/version: v8.38.0 # {"lp-deploy-tag-updater:version": "website-api-gateway"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
      containers:
        - name: website-api-gateway
          resources:
            limits:
              cpu: "1.5"
              memory: "2Gi"
            requests:
              cpu: "1.1"
              memory: "2Gi"
          env:
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: UV_THREADPOOL_SIZE
              value: "8"
