apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - target:
      group: batch
      kind: Job
      name: website-api-gateway-static-cp-job
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: website-api-gateway-static-cp-job-prod-
  - path: ingress.yaml
  - path: deploy.yaml
  - path: deploy-consumer.yaml
  - path: pdb.yaml
  - path: hpa.yaml
  - path: hpa-consumer.yaml
  - path: sa.yaml
  - path: static-cp-job.yaml
  - path: static-cp-job-old.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/website-api-gateway
    newTag: v8.37.7 # {"lp-deploy-tag-updater:version": "website-api-gateway"}
transformers:
  - version-suffixer.yaml
