apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-automation-service-deployment
  labels:
    app: WorkflowAutomationService
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.3.0 # {"lp-deploy-tag-updater:version": "workflow-automation-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: workflow-automation-service-sa-prod
      labels:
        app: WorkflowAutomationService
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.3.0 # {"lp-deploy-tag-updater:version": "workflow-automation-service"}
