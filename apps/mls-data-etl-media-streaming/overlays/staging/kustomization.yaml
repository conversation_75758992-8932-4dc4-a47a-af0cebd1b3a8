apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - mls-data-etl-media-streaming-dd-metric.yaml
patches:
  - path: deploy.yaml
  - path: dlq-deploy.yaml
  - path: sa.yaml
  - path: hpa.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-media-streaming
    newTag: main-14893-afceb7b # {"$imagepolicy": "flux-system:mls-data-etl-media-streaming-flux-image-policy-staging-new:tag"}
