apiVersion: batch/v1
kind: Job
metadata:
  name: load-audit-log-service-migration
spec:
  template:
    metadata:
    spec:
      containers:
        - name: load-audit-log-service-migration
          image: luxurypresence/load-audit-log-service-migration
          env:
            - name: HOST
              value: '${vault:secret/data/staging/audit-log-service#DB_HOST}'
            - name: PORT
              value: "5432"
            - name: DB
              value: '${vault:secret/data/staging/audit-log-service#DB}'
            - name: FLYWAY_PASSWORD
              value: '${vault:secret/data/staging/audit-log-service#DB_PASSWORD}'
            - name: FLYWAY_USER
              value: '${vault:secret/data/staging/audit-log-service#DB_USERNAME}'
