apiVersion: batch/v1
kind: Job
metadata:
  name: load-audit-log-service-migration
spec:
  template:
    metadata:
    spec:
      serviceAccountName: audit-log-service
      containers:
        - name: load-audit-log-service-migration
          image: luxurypresence/load-audit-log-service-migration
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:secret/data/staging/audit-log-service#DB_USERNAME}:${vault:secret/data/staging/audit-log-service#DB_PASSWORD}@${vault:secret/data/staging/audit-log-service#DB_HOST}:5432/${vault:secret/data/staging/audit-log-service#DB}'
