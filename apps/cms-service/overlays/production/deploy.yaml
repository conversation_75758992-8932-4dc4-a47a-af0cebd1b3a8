apiVersion: apps/v1
kind: Deployment
metadata:
  name: cms-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v4.19.0 # {"lp-deploy-tag-updater:version": "cms-service"}
    tags.datadoghq.com/profiling: 'false'
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: cms-service-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v4.19.0 # {"lp-deploy-tag-updater:version": "cms-service"}
        tags.datadoghq.com/profiling: 'false'
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
          env:
            - name: DATABASE_NAME
              value: vault:secret/data/production/database-info/main-v3#DB
              valueFrom:
                $patch: delete
      containers:
        - name: cms-service
          env:
            - name: SEQUELIZE_REPLICATION
              value: '{"read":[{"host":"${vault:secret/data/production/database-info/main-v3#READER_HOST}","username":"${vault:database/creds/postgres-production-property-long-life-v3#username}","password":"${vault:database/creds/postgres-production-property-long-life-v3#password}"}],"write":{"host":"${vault:secret/data/production/database-info/main-v3#WRITER_HOST}","username":"${vault:database/creds/postgres-production-property-long-life-v3#username}","password":"${vault:database/creds/postgres-production-property-long-life-v3#password}"}}'
            - name: VAULT_DISASTER_RECOVERY_PG_CONNECTION_STRING
              $patch: delete
            - name: DD_PROFILING_ENABLED
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/profiling']
          resources:
            limits:
              cpu: "2"
              memory: "1800Mi"
            requests:
              cpu: "800m"
              memory: "1800Mi"
