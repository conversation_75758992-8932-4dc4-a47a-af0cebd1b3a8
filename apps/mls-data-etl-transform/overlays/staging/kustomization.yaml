apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
transformers:
  - version-suffixer.yaml
images:
  - name: luxurypresence/data-etl-transform
    newTag: main-14892-dc2c5c4 # {"$imagepolicy": "flux-system:mls-data-etl-transform-flux-image-policy-staging-new:tag"}
patches:
  - path: job.yaml
  - path: sa.yaml
  - target:
      group: batch
      kind: Job
      name: mls-data-etl-transform-job
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-etl-transform-job-staging-
