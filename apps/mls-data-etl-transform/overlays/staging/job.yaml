apiVersion: batch/v1
kind: Job
metadata:
  name: mls-data-etl-transform-job
spec:
  template:
    spec:
      containers:
        - name: aws-cli
          env:
            - name: TRANSFORM_BUCKET
              value: "lp-datalakehouse-stage"
            - name: ENV
              value: "staging"
            - name: VERSION
              value: main-14892-dc2c5c4 # {"$imagepolicy": "flux-system:mls-data-etl-transform-flux-image-policy-staging-new:tag"}
            - name: ORCHESTRATOR_URL
              value: https://mls-data-etl-streaming-orchestrator.luxurycoders.com
