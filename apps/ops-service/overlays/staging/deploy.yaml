apiVersion: apps/v1
kind: Deployment
metadata:
  name: ops-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v0.23.2 # {"lp-deploy-tag-updater:version": "ops-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v0.23.2 # {"lp-deploy-tag-updater:version": "ops-service"}
    spec:
      containers:
        - name: ops-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-ops-service-long-life-v4#username}:${vault:database/creds/postgres-staging-ops-service-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-ops-service-long-life-v4#username}:${vault:database/creds/postgres-staging-ops-service-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
            - name: JOB_TYPE
              value: SERVICE_DEPLOYMENT
