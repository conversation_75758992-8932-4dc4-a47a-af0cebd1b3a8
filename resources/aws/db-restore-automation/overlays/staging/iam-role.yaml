apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  name: db-restore-automation-role
spec:
  forProvider:
    maxSessionDuration: 14400
    assumeRolePolicy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Principal": {
                      "Federated": "arn:aws:iam::093949242303:oidc-provider/oidc.circleci.com/org/fe3be603-aa6a-4106-913d-323a14c5e951"
                  },
                  "Action": "sts:AssumeRoleWithWebIdentity",
                  "Condition": {
                      "StringEquals": {
                          "oidc.circleci.com/org/fe3be603-aa6a-4106-913d-323a14c5e951:aud": "fe3be603-aa6a-4106-913d-323a14c5e951"
                      }
                  }
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
